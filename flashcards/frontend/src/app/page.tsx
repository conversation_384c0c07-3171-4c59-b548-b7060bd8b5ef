'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Deck } from 'shared';
import { api } from '@/lib/api';
import ProtectedRoute from '@/components/ProtectedRoute';
import UserHeader from '@/components/UserHeader';

export default function HomePage() {
  const [decks, setDecks] = useState<Deck[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadDecks() {
      try {
        const data = await api.decks.getAll();
        setDecks(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load decks');
      } finally {
        setLoading(false);
      }
    }

    loadDecks();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading decks...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️ Error</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <UserHeader />
        <div className="container mx-auto px-4 py-8">
          <header className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome Back!</h1>
            <p className="text-lg text-gray-600">
              Continue your bird identification journey
            </p>
          </header>

        <main>
          {decks.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📚</div>
              <h2 className="text-2xl font-semibold text-gray-700 mb-2">No decks available</h2>
              <p className="text-gray-500 mb-6">
                Create your first flashcard deck to get started with bird identification practice.
              </p>
              <Link 
                href="/decks/create"
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                Create Your First Deck
              </Link>
            </div>
          ) : (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-gray-800">
                  Your Decks ({decks.length} available)
                </h2>
                <Link 
                  href="/decks/create"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  + Create New Deck
                </Link>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {decks.map((deck) => (
                  <Link
                    key={deck.id}
                    href={`/decks/${deck.id}`}
                    className="group block"
                  >
                    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 p-6 border border-gray-200 group-hover:border-blue-300">
                      <div className="flex items-start justify-between mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600">
                          {deck.name}
                        </h3>
                        <div className="text-2xl">🃏</div>
                      </div>
                      
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {deck.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                          Created {new Date(deck.createdAt).toLocaleDateString()}
                        </span>
                        <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                          Start Review →
                        </span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}