"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Deck = void 0;
const typeorm_1 = require("typeorm");
const flashcard_entity_1 = require("./flashcard.entity");
const review_session_entity_1 = require("./review-session.entity");
const user_entity_1 = require("./user.entity");
let Deck = class Deck {
};
exports.Deck = Deck;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Deck.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Deck.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Deck.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Deck.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], Deck.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.decks),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], Deck.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => flashcard_entity_1.Flashcard, flashcard => flashcard.deck),
    __metadata("design:type", Array)
], Deck.prototype, "flashcards", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => review_session_entity_1.ReviewSession, session => session.deck),
    __metadata("design:type", Array)
], Deck.prototype, "reviewSessions", void 0);
exports.Deck = Deck = __decorate([
    (0, typeorm_1.Entity)('decks')
], Deck);
//# sourceMappingURL=deck.entity.js.map