import { Request } from 'express';
import { DecksService } from './decks.service';
import { CreateDeckDto, UpdateDeckDto, CreateFlashcardDto, UpdateFlashcardDto } from 'shared';
export declare class DecksController {
    private readonly decksService;
    constructor(decksService: DecksService);
    findAll(req: Request): Promise<import("../entities").Deck[]>;
    create(createDeckDto: CreateDeckDto, req: Request): Promise<import("../entities").Deck>;
    findOne(id: string, req: Request): Promise<import("../entities").Deck>;
    update(id: string, updateDeckDto: UpdateDeckDto, req: Request): Promise<import("../entities").Deck>;
    remove(id: string, req: Request): Promise<void>;
    getFlashcards(id: string, req: Request): Promise<import("../entities").Flashcard[]>;
    createFlashcard(id: string, createFlashcardDto: CreateFlashcardDto, req: Request): Promise<import("../entities").Flashcard>;
    updateFlashcard(deckId: string, flashcardId: string, updateFlashcardDto: UpdateFlashcardDto, req: Request): Promise<import("../entities").Flashcard>;
    removeFlashcard(deckId: string, flashcardId: string, req: Request): Promise<void>;
}
