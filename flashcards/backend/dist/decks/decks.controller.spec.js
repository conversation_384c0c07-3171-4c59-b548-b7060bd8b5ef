"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const decks_controller_1 = require("./decks.controller");
const decks_service_1 = require("./decks.service");
describe('DecksController', () => {
    let controller;
    let service;
    const mockUser = {
        id: 'user-1',
        googleId: 'google-123',
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'test.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
        decks: [],
        reviewSessions: [],
    };
    const mockDeck = {
        id: '1',
        name: 'Test Deck',
        description: 'A test deck',
        createdAt: new Date(),
        userId: 'user-1',
        user: mockUser,
        flashcards: [],
        reviewSessions: [],
    };
    const mockFlashcard = {
        id: '1',
        deckId: '1',
        birdName: 'Test Bird',
        imageUrl: 'test.jpg',
        createdAt: new Date(),
        deck: mockDeck,
        reviewResults: [],
    };
    const mockDecksService = {
        findAll: jest.fn(),
        findOne: jest.fn(),
        findByUserId: jest.fn(),
        findOneByUser: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
        getFlashcards: jest.fn(),
        createFlashcard: jest.fn(),
        updateFlashcard: jest.fn(),
        removeFlashcard: jest.fn(),
    };
    const mockRequest = {
        user: mockUser,
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [decks_controller_1.DecksController],
            providers: [
                {
                    provide: decks_service_1.DecksService,
                    useValue: mockDecksService,
                },
            ],
        }).compile();
        controller = module.get(decks_controller_1.DecksController);
        service = module.get(decks_service_1.DecksService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it('should be defined', () => {
        expect(controller).toBeDefined();
    });
    describe('findAll', () => {
        it('should return user decks', async () => {
            const expectedDecks = [mockDeck];
            mockDecksService.findByUserId.mockResolvedValue(expectedDecks);
            const result = await controller.findAll(mockRequest);
            expect(result).toEqual(expectedDecks);
            expect(service.findByUserId).toHaveBeenCalledWith('user-1');
        });
    });
    describe('create', () => {
        it('should create a new deck', async () => {
            const createDto = { name: 'New Deck', description: 'Description' };
            mockDecksService.create.mockResolvedValue(mockDeck);
            const result = await controller.create(createDto, mockRequest);
            expect(result).toEqual(mockDeck);
            expect(service.create).toHaveBeenCalledWith(createDto, 'user-1');
        });
    });
    describe('findOne', () => {
        it('should return a specific deck', async () => {
            mockDecksService.findOneByUser.mockResolvedValue(mockDeck);
            const result = await controller.findOne('1', mockRequest);
            expect(result).toEqual(mockDeck);
            expect(service.findOneByUser).toHaveBeenCalledWith('1', 'user-1');
        });
    });
    describe('update', () => {
        it('should update a deck', async () => {
            const updateDto = { name: 'Updated Deck' };
            mockDecksService.update.mockResolvedValue(mockDeck);
            const result = await controller.update('1', updateDto, mockRequest);
            expect(result).toEqual(mockDeck);
            expect(service.update).toHaveBeenCalledWith('1', updateDto, 'user-1');
        });
    });
    describe('remove', () => {
        it('should remove a deck', async () => {
            mockDecksService.remove.mockResolvedValue(undefined);
            await controller.remove('1', mockRequest);
            expect(service.remove).toHaveBeenCalledWith('1', 'user-1');
        });
    });
    describe('getFlashcards', () => {
        it('should return flashcards for a deck', async () => {
            const expectedFlashcards = [mockFlashcard];
            mockDecksService.getFlashcards.mockResolvedValue(expectedFlashcards);
            const result = await controller.getFlashcards('1', mockRequest);
            expect(result).toEqual(expectedFlashcards);
            expect(service.getFlashcards).toHaveBeenCalledWith('1', 'user-1');
        });
    });
});
//# sourceMappingURL=decks.controller.spec.js.map