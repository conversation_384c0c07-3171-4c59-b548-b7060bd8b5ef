"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const decks_service_1 = require("./decks.service");
const entities_1 = require("../entities");
describe('DecksService', () => {
    let service;
    let decksRepository;
    let flashcardsRepository;
    const mockUser = {
        id: 'user-1',
        googleId: 'google-123',
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'test.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
        decks: [],
        reviewSessions: [],
    };
    const mockDeck = {
        id: '1',
        name: 'Test Deck',
        description: 'A test deck',
        createdAt: new Date(),
        userId: 'user-1',
        user: mockUser,
        flashcards: [],
        reviewSessions: [],
    };
    const mockFlashcard = {
        id: '1',
        deckId: '1',
        birdName: 'Test Bird',
        imageUrl: 'test.jpg',
        createdAt: new Date(),
        deck: mockDeck,
        reviewResults: [],
    };
    const mockDecksRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        remove: jest.fn(),
    };
    const mockFlashcardsRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        remove: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                decks_service_1.DecksService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(entities_1.Deck),
                    useValue: mockDecksRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(entities_1.Flashcard),
                    useValue: mockFlashcardsRepository,
                },
            ],
        }).compile();
        service = module.get(decks_service_1.DecksService);
        decksRepository = module.get((0, typeorm_1.getRepositoryToken)(entities_1.Deck));
        flashcardsRepository = module.get((0, typeorm_1.getRepositoryToken)(entities_1.Flashcard));
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('findByUserId', () => {
        it('should return decks for a user', async () => {
            const expectedDecks = [mockDeck];
            mockDecksRepository.find.mockResolvedValue(expectedDecks);
            const result = await service.findByUserId('user-1');
            expect(result).toEqual(expectedDecks);
            expect(mockDecksRepository.find).toHaveBeenCalledWith({
                where: { userId: 'user-1' },
                order: { createdAt: 'DESC' }
            });
        });
    });
    describe('create', () => {
        it('should create a new deck', async () => {
            const createDto = { name: 'New Deck', description: 'Description' };
            mockDecksRepository.create.mockReturnValue(mockDeck);
            mockDecksRepository.save.mockResolvedValue(mockDeck);
            const result = await service.create(createDto, 'user-1');
            expect(result).toEqual(mockDeck);
            expect(mockDecksRepository.create).toHaveBeenCalledWith({
                ...createDto,
                userId: 'user-1',
            });
            expect(mockDecksRepository.save).toHaveBeenCalledWith(mockDeck);
        });
    });
    describe('findOneByUser', () => {
        it('should return a deck by id and user', async () => {
            mockDecksRepository.findOne.mockResolvedValue(mockDeck);
            const result = await service.findOneByUser('1', 'user-1');
            expect(result).toEqual(mockDeck);
            expect(mockDecksRepository.findOne).toHaveBeenCalledWith({
                where: { id: '1', userId: 'user-1' },
                relations: ['flashcards']
            });
        });
        it('should throw NotFoundException if deck not found', async () => {
            mockDecksRepository.findOne.mockResolvedValue(null);
            await expect(service.findOneByUser('999', 'user-1')).rejects.toThrow('Deck not found');
        });
    });
    describe('update', () => {
        it('should update a deck', async () => {
            const updateDto = { name: 'Updated Deck' };
            mockDecksRepository.findOne.mockResolvedValue(mockDeck);
            mockDecksRepository.save.mockResolvedValue({ ...mockDeck, ...updateDto });
            const result = await service.update('1', updateDto, 'user-1');
            expect(result).toEqual({ ...mockDeck, ...updateDto });
        });
    });
    describe('remove', () => {
        it('should remove a deck', async () => {
            mockDecksRepository.findOne.mockResolvedValue(mockDeck);
            mockDecksRepository.remove.mockResolvedValue(mockDeck);
            await service.remove('1', 'user-1');
            expect(mockDecksRepository.remove).toHaveBeenCalledWith(mockDeck);
        });
    });
    describe('getFlashcards', () => {
        it('should return flashcards for a deck with user validation', async () => {
            const expectedFlashcards = [mockFlashcard];
            mockDecksRepository.findOne.mockResolvedValue(mockDeck);
            mockFlashcardsRepository.find.mockResolvedValue(expectedFlashcards);
            const result = await service.getFlashcards('1', 'user-1');
            expect(result).toEqual(expectedFlashcards);
            expect(mockFlashcardsRepository.find).toHaveBeenCalledWith({
                where: { deckId: '1' },
                order: { createdAt: 'ASC' }
            });
        });
        it('should return flashcards without user validation', async () => {
            const expectedFlashcards = [mockFlashcard];
            mockFlashcardsRepository.find.mockResolvedValue(expectedFlashcards);
            const result = await service.getFlashcards('1');
            expect(result).toEqual(expectedFlashcards);
            expect(mockFlashcardsRepository.find).toHaveBeenCalledWith({
                where: { deckId: '1' },
                order: { createdAt: 'ASC' }
            });
        });
    });
});
//# sourceMappingURL=decks.service.spec.js.map