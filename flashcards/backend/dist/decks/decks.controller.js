"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecksController = void 0;
const common_1 = require("@nestjs/common");
const decks_service_1 = require("./decks.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
let DecksController = class DecksController {
    constructor(decksService) {
        this.decksService = decksService;
    }
    findAll(req) {
        const user = req.user;
        return this.decksService.findByUserId(user.id);
    }
    create(createDeckDto, req) {
        const user = req.user;
        return this.decksService.create(createDeckDto, user.id);
    }
    findOne(id, req) {
        const user = req.user;
        return this.decksService.findOneByUser(id, user.id);
    }
    update(id, updateDeckDto, req) {
        const user = req.user;
        return this.decksService.update(id, updateDeckDto, user.id);
    }
    remove(id, req) {
        const user = req.user;
        return this.decksService.remove(id, user.id);
    }
    getFlashcards(id, req) {
        const user = req.user;
        return this.decksService.getFlashcards(id, user.id);
    }
    createFlashcard(id, createFlashcardDto, req) {
        const user = req.user;
        return this.decksService.createFlashcard(id, createFlashcardDto, user.id);
    }
    updateFlashcard(deckId, flashcardId, updateFlashcardDto, req) {
        const user = req.user;
        return this.decksService.updateFlashcard(deckId, flashcardId, updateFlashcardDto, user.id);
    }
    removeFlashcard(deckId, flashcardId, req) {
        const user = req.user;
        return this.decksService.removeFlashcard(deckId, flashcardId, user.id);
    }
};
exports.DecksController = DecksController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':id/flashcards'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "getFlashcards", null);
__decorate([
    (0, common_1.Post)(':id/flashcards'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "createFlashcard", null);
__decorate([
    (0, common_1.Put)(':deckId/flashcards/:flashcardId'),
    __param(0, (0, common_1.Param)('deckId')),
    __param(1, (0, common_1.Param)('flashcardId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "updateFlashcard", null);
__decorate([
    (0, common_1.Delete)(':deckId/flashcards/:flashcardId'),
    __param(0, (0, common_1.Param)('deckId')),
    __param(1, (0, common_1.Param)('flashcardId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", void 0)
], DecksController.prototype, "removeFlashcard", null);
exports.DecksController = DecksController = __decorate([
    (0, common_1.Controller)('decks'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [decks_service_1.DecksService])
], DecksController);
//# sourceMappingURL=decks.controller.js.map