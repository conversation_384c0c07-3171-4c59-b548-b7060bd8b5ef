"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
let DecksService = class DecksService {
    constructor(decksRepository, flashcardsRepository) {
        this.decksRepository = decksRepository;
        this.flashcardsRepository = flashcardsRepository;
    }
    findAll() {
        return this.decksRepository.find();
    }
    findOne(id) {
        return this.decksRepository.findOne({ where: { id } });
    }
    async findByUserId(userId) {
        return this.decksRepository.find({
            where: { userId },
            order: { createdAt: 'DESC' }
        });
    }
    async create(createDeckDto, userId) {
        const deck = this.decksRepository.create({
            ...createDeckDto,
            userId,
        });
        return this.decksRepository.save(deck);
    }
    async findOneByUser(id, userId) {
        const deck = await this.decksRepository.findOne({
            where: { id, userId },
            relations: ['flashcards']
        });
        if (!deck) {
            throw new common_1.NotFoundException('Deck not found');
        }
        return deck;
    }
    async update(id, updateDeckDto, userId) {
        const deck = await this.findOneByUser(id, userId);
        Object.assign(deck, updateDeckDto);
        return this.decksRepository.save(deck);
    }
    async remove(id, userId) {
        const deck = await this.findOneByUser(id, userId);
        await this.decksRepository.remove(deck);
    }
    async getFlashcards(deckId, userId) {
        if (userId) {
            await this.findOneByUser(deckId, userId);
        }
        return this.flashcardsRepository.find({
            where: { deckId },
            order: { createdAt: 'ASC' }
        });
    }
    async createFlashcard(deckId, createFlashcardDto, userId) {
        await this.findOneByUser(deckId, userId);
        const flashcard = this.flashcardsRepository.create({
            ...createFlashcardDto,
            deckId,
        });
        return this.flashcardsRepository.save(flashcard);
    }
    async updateFlashcard(deckId, flashcardId, updateFlashcardDto, userId) {
        await this.findOneByUser(deckId, userId);
        const flashcard = await this.flashcardsRepository.findOne({
            where: { id: flashcardId, deckId }
        });
        if (!flashcard) {
            throw new common_1.NotFoundException('Flashcard not found');
        }
        Object.assign(flashcard, updateFlashcardDto);
        return this.flashcardsRepository.save(flashcard);
    }
    async removeFlashcard(deckId, flashcardId, userId) {
        await this.findOneByUser(deckId, userId);
        const flashcard = await this.flashcardsRepository.findOne({
            where: { id: flashcardId, deckId }
        });
        if (!flashcard) {
            throw new common_1.NotFoundException('Flashcard not found');
        }
        await this.flashcardsRepository.remove(flashcard);
    }
};
exports.DecksService = DecksService;
exports.DecksService = DecksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.Deck)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.Flashcard)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], DecksService);
//# sourceMappingURL=decks.service.js.map