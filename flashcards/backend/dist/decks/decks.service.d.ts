import { Repository } from 'typeorm';
import { Deck, Flashcard } from '../entities';
import { CreateDeckDto, UpdateDeckDto, CreateFlashcardDto, UpdateFlashcardDto } from 'shared';
export declare class DecksService {
    private decksRepository;
    private flashcardsRepository;
    constructor(decksRepository: Repository<Deck>, flashcardsRepository: Repository<Flashcard>);
    findAll(): Promise<Deck[]>;
    findOne(id: string): Promise<Deck>;
    findByUserId(userId: string): Promise<Deck[]>;
    create(createDeckDto: CreateDeckDto, userId: string): Promise<Deck>;
    findOneByUser(id: string, userId: string): Promise<Deck>;
    update(id: string, updateDeckDto: UpdateDeckDto, userId: string): Promise<Deck>;
    remove(id: string, userId: string): Promise<void>;
    getFlashcards(deckId: string, userId?: string): Promise<Flashcard[]>;
    createFlashcard(deckId: string, createFlashcardDto: CreateFlashcardDto, userId: string): Promise<Flashcard>;
    updateFlashcard(deckId: string, flashcardId: string, updateFlashcardDto: UpdateFlashcardDto, userId: string): Promise<Flashcard>;
    removeFlashcard(deckId: string, flashcardId: string, userId: string): Promise<void>;
}
