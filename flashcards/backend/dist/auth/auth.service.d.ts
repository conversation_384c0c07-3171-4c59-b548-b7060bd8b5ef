import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { User } from '../entities/user.entity';
import { LoginResponse } from 'shared';
interface CreateUserData {
    googleId: string;
    email: string;
    name: string;
    picture?: string;
}
export declare class AuthService {
    private userRepository;
    private jwtService;
    constructor(userRepository: Repository<User>, jwtService: JwtService);
    findOrCreateUser(userData: CreateUserData): Promise<User>;
    validateUser(userId: string): Promise<User | null>;
    login(user: User): Promise<LoginResponse>;
}
export {};
