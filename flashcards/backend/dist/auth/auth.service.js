"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jwt_1 = require("@nestjs/jwt");
const user_entity_1 = require("../entities/user.entity");
let AuthService = class AuthService {
    constructor(userRepository, jwtService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
    }
    async findOrCreateUser(userData) {
        let user = await this.userRepository.findOne({
            where: { googleId: userData.googleId },
        });
        if (!user) {
            user = this.userRepository.create(userData);
            await this.userRepository.save(user);
        }
        else {
            user.email = userData.email;
            user.name = userData.name;
            user.picture = userData.picture;
            await this.userRepository.save(user);
        }
        return user;
    }
    async validateUser(userId) {
        return this.userRepository.findOne({
            where: { id: userId },
        });
    }
    async login(user) {
        const payload = {
            sub: user.id,
            email: user.email,
            name: user.name,
            picture: user.picture,
        };
        const authUser = {
            id: user.id,
            email: user.email,
            name: user.name,
            picture: user.picture,
        };
        return {
            access_token: this.jwtService.sign(payload),
            user: authUser,
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map