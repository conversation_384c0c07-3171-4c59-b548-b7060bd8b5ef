import { Request, Response } from 'express';
import { AuthService } from './auth.service';
import { User } from '../entities/user.entity';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    googleAuth(req: Request): Promise<void>;
    googleAuthRedirect(req: Request, res: Response): Promise<void>;
    getProfile(req: Request): Promise<User>;
    logout(): Promise<{
        message: string;
    }>;
}
