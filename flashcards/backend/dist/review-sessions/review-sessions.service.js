"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewSessionsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
let ReviewSessionsService = class ReviewSessionsService {
    constructor(reviewSessionsRepository, reviewResultsRepository, decksRepository, flashcardsRepository) {
        this.reviewSessionsRepository = reviewSessionsRepository;
        this.reviewResultsRepository = reviewResultsRepository;
        this.decksRepository = decksRepository;
        this.flashcardsRepository = flashcardsRepository;
    }
    async create(createReviewSessionDto, userId) {
        if (userId) {
            const deck = await this.decksRepository.findOne({
                where: { id: createReviewSessionDto.deckId, userId }
            });
            if (!deck) {
                throw new common_1.NotFoundException('Deck not found');
            }
        }
        const session = this.reviewSessionsRepository.create({
            deckId: createReviewSessionDto.deckId,
            userId,
            startedAt: new Date(),
        });
        return this.reviewSessionsRepository.save(session);
    }
    findOne(id) {
        return this.reviewSessionsRepository.findOne({
            where: { id },
            relations: ['deck', 'reviewResults', 'reviewResults.flashcard']
        });
    }
    async findOneByUser(id, userId) {
        const session = await this.reviewSessionsRepository.findOne({
            where: { id, userId },
            relations: ['deck', 'reviewResults', 'reviewResults.flashcard']
        });
        if (!session) {
            throw new common_1.NotFoundException('Review session not found');
        }
        return session;
    }
    async submitResult(sessionId, submitResultDto, userId) {
        if (userId) {
            await this.findOneByUser(sessionId, userId);
        }
        const result = this.reviewResultsRepository.create({
            sessionId,
            flashcardId: submitResultDto.flashcardId,
            difficultyRating: submitResultDto.difficultyRating,
            createdAt: new Date(),
        });
        return this.reviewResultsRepository.save(result);
    }
    async complete(id, userId) {
        if (userId) {
            await this.findOneByUser(id, userId);
        }
        await this.reviewSessionsRepository.update(id, {
            completedAt: new Date(),
        });
        return userId ? this.findOneByUser(id, userId) : this.findOne(id);
    }
    async getSummary(sessionId, userId) {
        const session = userId
            ? await this.findOneByUser(sessionId, userId)
            : await this.reviewSessionsRepository.findOne({
                where: { id: sessionId },
                relations: ['deck', 'reviewResults', 'reviewResults.flashcard']
            });
        if (!session) {
            return null;
        }
        const easy = session.reviewResults
            .filter(r => r.difficultyRating === entities_1.DifficultyRating.EASY)
            .map(r => r.flashcard);
        const difficult = session.reviewResults
            .filter(r => r.difficultyRating === entities_1.DifficultyRating.DIFFICULT)
            .map(r => r.flashcard);
        const incorrect = session.reviewResults
            .filter(r => r.difficultyRating === entities_1.DifficultyRating.INCORRECT)
            .map(r => r.flashcard);
        return {
            sessionId: session.id,
            deckName: session.deck.name,
            totalCards: session.reviewResults.length,
            easy,
            difficult,
            incorrect,
            completedAt: session.completedAt,
        };
    }
};
exports.ReviewSessionsService = ReviewSessionsService;
exports.ReviewSessionsService = ReviewSessionsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.ReviewSession)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.ReviewResult)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.Deck)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.Flashcard)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ReviewSessionsService);
//# sourceMappingURL=review-sessions.service.js.map