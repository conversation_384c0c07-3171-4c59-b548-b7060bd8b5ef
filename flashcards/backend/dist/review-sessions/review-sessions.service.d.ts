import { Repository } from 'typeorm';
import { ReviewSession, ReviewResult, Deck, Flashcard } from '../entities';
import { CreateReviewSessionDto, SubmitResultDto } from './dto';
export declare class ReviewSessionsService {
    private reviewSessionsRepository;
    private reviewResultsRepository;
    private decksRepository;
    private flashcardsRepository;
    constructor(reviewSessionsRepository: Repository<ReviewSession>, reviewResultsRepository: Repository<ReviewResult>, decksRepository: Repository<Deck>, flashcardsRepository: Repository<Flashcard>);
    create(createReviewSessionDto: CreateReviewSessionDto, userId?: string): Promise<ReviewSession>;
    findOne(id: string): Promise<ReviewSession>;
    findOneByUser(id: string, userId: string): Promise<ReviewSession>;
    submitResult(sessionId: string, submitResultDto: SubmitResultDto, userId?: string): Promise<ReviewResult>;
    complete(id: string, userId?: string): Promise<ReviewSession>;
    getSummary(sessionId: string, userId?: string): Promise<{
        sessionId: string;
        deckName: string;
        totalCards: number;
        easy: Flashcard[];
        difficult: Flashcard[];
        incorrect: Flashcard[];
        completedAt: Date;
    }>;
}
