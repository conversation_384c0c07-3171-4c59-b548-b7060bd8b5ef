"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const review_sessions_service_1 = require("./review-sessions.service");
const entities_1 = require("../entities");
describe('ReviewSessionsService', () => {
    let service;
    let reviewSessionsRepository;
    let reviewResultsRepository;
    let decksRepository;
    let flashcardsRepository;
    const mockUser = {
        id: 'user-1',
        googleId: 'google-123',
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'test.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
        decks: [],
        reviewSessions: [],
    };
    const mockDeck = {
        id: '1',
        name: 'Test Deck',
        description: 'A test deck',
        createdAt: new Date(),
        userId: 'user-1',
        user: mockUser,
        flashcards: [],
        reviewSessions: [],
    };
    const mockFlashcard = {
        id: '1',
        deckId: '1',
        birdName: 'Test Bird',
        imageUrl: 'test.jpg',
        createdAt: new Date(),
        deck: mockDeck,
        reviewResults: [],
    };
    const mockReviewSession = {
        id: '1',
        deckId: '1',
        userId: 'user-1',
        startedAt: new Date(),
        completedAt: undefined,
        user: mockUser,
        deck: mockDeck,
        reviewResults: [],
    };
    const mockReviewResult = {
        id: '1',
        sessionId: '1',
        flashcardId: '1',
        difficultyRating: entities_1.DifficultyRating.EASY,
        createdAt: new Date(),
        session: mockReviewSession,
        flashcard: mockFlashcard,
    };
    const mockRepositories = {
        reviewSessions: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
        },
        reviewResults: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
        },
        decks: {
            findOne: jest.fn(),
        },
        flashcards: {
            find: jest.fn(),
        },
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                review_sessions_service_1.ReviewSessionsService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(entities_1.ReviewSession),
                    useValue: mockRepositories.reviewSessions,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(entities_1.ReviewResult),
                    useValue: mockRepositories.reviewResults,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(entities_1.Deck),
                    useValue: mockRepositories.decks,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(entities_1.Flashcard),
                    useValue: mockRepositories.flashcards,
                },
            ],
        }).compile();
        service = module.get(review_sessions_service_1.ReviewSessionsService);
        reviewSessionsRepository = module.get((0, typeorm_1.getRepositoryToken)(entities_1.ReviewSession));
        reviewResultsRepository = module.get((0, typeorm_1.getRepositoryToken)(entities_1.ReviewResult));
        decksRepository = module.get((0, typeorm_1.getRepositoryToken)(entities_1.Deck));
        flashcardsRepository = module.get((0, typeorm_1.getRepositoryToken)(entities_1.Flashcard));
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('create', () => {
        it('should create a new review session', async () => {
            const createDto = { deckId: '1' };
            const expectedSession = { ...mockReviewSession };
            mockRepositories.decks.findOne.mockResolvedValue(mockDeck);
            mockRepositories.reviewSessions.create.mockReturnValue(expectedSession);
            mockRepositories.reviewSessions.save.mockResolvedValue(expectedSession);
            const result = await service.create(createDto, 'user-1');
            expect(result).toEqual(expectedSession);
            expect(mockRepositories.decks.findOne).toHaveBeenCalledWith({
                where: { id: '1', userId: 'user-1' }
            });
            expect(mockRepositories.reviewSessions.create).toHaveBeenCalledWith({
                deckId: '1',
                userId: 'user-1',
                startedAt: expect.any(Date),
            });
            expect(mockRepositories.reviewSessions.save).toHaveBeenCalledWith(expectedSession);
        });
    });
    describe('findOneByUser', () => {
        it('should return a review session with user validation', async () => {
            const sessionWithRelations = {
                ...mockReviewSession,
                deck: mockDeck,
                reviewResults: [mockReviewResult],
            };
            mockRepositories.reviewSessions.findOne.mockResolvedValue(sessionWithRelations);
            const result = await service.findOneByUser('1', 'user-1');
            expect(result).toEqual(sessionWithRelations);
            expect(mockRepositories.reviewSessions.findOne).toHaveBeenCalledWith({
                where: { id: '1', userId: 'user-1' },
                relations: ['deck', 'reviewResults', 'reviewResults.flashcard']
            });
        });
    });
    describe('submitResult', () => {
        it('should create and save a review result with user validation', async () => {
            const submitDto = {
                flashcardId: '1',
                difficultyRating: entities_1.DifficultyRating.EASY,
            };
            mockRepositories.reviewSessions.findOne.mockResolvedValue(mockReviewSession);
            mockRepositories.reviewResults.create.mockReturnValue(mockReviewResult);
            mockRepositories.reviewResults.save.mockResolvedValue(mockReviewResult);
            const result = await service.submitResult('1', submitDto, 'user-1');
            expect(result).toEqual(mockReviewResult);
            expect(mockRepositories.reviewSessions.findOne).toHaveBeenCalledWith({
                where: { id: '1', userId: 'user-1' },
                relations: ['deck', 'reviewResults', 'reviewResults.flashcard']
            });
            expect(mockRepositories.reviewResults.create).toHaveBeenCalledWith({
                sessionId: '1',
                flashcardId: '1',
                difficultyRating: entities_1.DifficultyRating.EASY,
                createdAt: expect.any(Date),
            });
            expect(mockRepositories.reviewResults.save).toHaveBeenCalledWith(mockReviewResult);
        });
    });
    describe('complete', () => {
        it('should mark session as complete with user validation', async () => {
            const completedSession = {
                ...mockReviewSession,
                completedAt: new Date(),
            };
            mockRepositories.reviewSessions.findOne.mockResolvedValueOnce(mockReviewSession);
            mockRepositories.reviewSessions.update.mockResolvedValue({ affected: 1 });
            mockRepositories.reviewSessions.findOne.mockResolvedValueOnce(completedSession);
            const result = await service.complete('1', 'user-1');
            expect(result).toEqual(completedSession);
            expect(mockRepositories.reviewSessions.findOne).toHaveBeenCalledWith({
                where: { id: '1', userId: 'user-1' },
                relations: ['deck', 'reviewResults', 'reviewResults.flashcard']
            });
            expect(mockRepositories.reviewSessions.update).toHaveBeenCalledWith('1', {
                completedAt: expect.any(Date),
            });
        });
    });
    describe('getSummary', () => {
        it('should return session summary with categorized results and user validation', async () => {
            const sessionWithResults = {
                ...mockReviewSession,
                deck: mockDeck,
                reviewResults: [
                    { ...mockReviewResult, difficultyRating: entities_1.DifficultyRating.EASY, flashcard: mockFlashcard },
                    {
                        ...mockReviewResult,
                        id: '2',
                        difficultyRating: entities_1.DifficultyRating.DIFFICULT,
                        flashcard: { ...mockFlashcard, id: '2', birdName: 'Difficult Bird' }
                    },
                    {
                        ...mockReviewResult,
                        id: '3',
                        difficultyRating: entities_1.DifficultyRating.INCORRECT,
                        flashcard: { ...mockFlashcard, id: '3', birdName: 'Incorrect Bird' }
                    },
                ],
                completedAt: new Date(),
            };
            mockRepositories.reviewSessions.findOne.mockResolvedValue(sessionWithResults);
            const result = await service.getSummary('1', 'user-1');
            expect(result).toEqual({
                sessionId: '1',
                deckName: 'Test Deck',
                totalCards: 3,
                easy: [mockFlashcard],
                difficult: [{ ...mockFlashcard, id: '2', birdName: 'Difficult Bird' }],
                incorrect: [{ ...mockFlashcard, id: '3', birdName: 'Incorrect Bird' }],
                completedAt: sessionWithResults.completedAt,
            });
            expect(mockRepositories.reviewSessions.findOne).toHaveBeenCalledWith({
                where: { id: '1', userId: 'user-1' },
                relations: ['deck', 'reviewResults', 'reviewResults.flashcard']
            });
        });
        it('should return null if session not found', async () => {
            mockRepositories.reviewSessions.findOne.mockResolvedValue(null);
            const result = await service.getSummary('999');
            expect(result).toBeNull();
        });
    });
});
//# sourceMappingURL=review-sessions.service.spec.js.map