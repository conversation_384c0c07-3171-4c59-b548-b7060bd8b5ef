"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewSessionsController = void 0;
const common_1 = require("@nestjs/common");
const review_sessions_service_1 = require("./review-sessions.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
let ReviewSessionsController = class ReviewSessionsController {
    constructor(reviewSessionsService) {
        this.reviewSessionsService = reviewSessionsService;
    }
    create(createReviewSessionDto, req) {
        const user = req.user;
        return this.reviewSessionsService.create(createReviewSessionDto, user.id);
    }
    findOne(id, req) {
        const user = req.user;
        return this.reviewSessionsService.findOneByUser(id, user.id);
    }
    submitResult(sessionId, submitResultDto, req) {
        const user = req.user;
        return this.reviewSessionsService.submitResult(sessionId, submitResultDto, user.id);
    }
    complete(id, req) {
        const user = req.user;
        return this.reviewSessionsService.complete(id, user.id);
    }
    getSummary(id, req) {
        const user = req.user;
        return this.reviewSessionsService.getSummary(id, user.id);
    }
};
exports.ReviewSessionsController = ReviewSessionsController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateReviewSessionDto, Object]),
    __metadata("design:returntype", void 0)
], ReviewSessionsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ReviewSessionsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(':id/results'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.SubmitResultDto, Object]),
    __metadata("design:returntype", void 0)
], ReviewSessionsController.prototype, "submitResult", null);
__decorate([
    (0, common_1.Patch)(':id/complete'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ReviewSessionsController.prototype, "complete", null);
__decorate([
    (0, common_1.Get)(':id/summary'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ReviewSessionsController.prototype, "getSummary", null);
exports.ReviewSessionsController = ReviewSessionsController = __decorate([
    (0, common_1.Controller)('review-sessions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [review_sessions_service_1.ReviewSessionsService])
], ReviewSessionsController);
//# sourceMappingURL=review-sessions.controller.js.map