import { Request } from 'express';
import { ReviewSessionsService } from './review-sessions.service';
import { CreateReviewSessionDto, SubmitResultDto } from './dto';
export declare class ReviewSessionsController {
    private readonly reviewSessionsService;
    constructor(reviewSessionsService: ReviewSessionsService);
    create(createReviewSessionDto: CreateReviewSessionDto, req: Request): Promise<import("../entities").ReviewSession>;
    findOne(id: string, req: Request): Promise<import("../entities").ReviewSession>;
    submitResult(sessionId: string, submitResultDto: SubmitResultDto, req: Request): Promise<import("../entities").ReviewResult>;
    complete(id: string, req: Request): Promise<import("../entities").ReviewSession>;
    getSummary(id: string, req: Request): Promise<{
        sessionId: string;
        deckName: string;
        totalCards: number;
        easy: import("../entities").Flashcard[];
        difficult: import("../entities").Flashcard[];
        incorrect: import("../entities").Flashcard[];
        completedAt: Date;
    }>;
}
