"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const review_sessions_controller_1 = require("./review-sessions.controller");
const review_sessions_service_1 = require("./review-sessions.service");
const entities_1 = require("../entities");
describe('ReviewSessionsController', () => {
    let controller;
    let service;
    const mockUser = {
        id: 'user-1',
        googleId: 'google-123',
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'test.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
        decks: [],
        reviewSessions: [],
    };
    const mockReviewSession = {
        id: '1',
        deckId: '1',
        startedAt: new Date(),
        completedAt: undefined,
        userId: 'user-1',
        user: mockUser,
        deck: undefined,
        reviewResults: [],
    };
    const mockSessionSummary = {
        sessionId: '1',
        deckName: 'Test Deck',
        totalCards: 3,
        easy: [],
        difficult: [],
        incorrect: [],
        completedAt: new Date(),
    };
    const mockReviewSessionsService = {
        create: jest.fn(),
        findOne: jest.fn(),
        findOneByUser: jest.fn(),
        submitResult: jest.fn(),
        complete: jest.fn(),
        getSummary: jest.fn(),
    };
    const mockRequest = {
        user: mockUser,
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [review_sessions_controller_1.ReviewSessionsController],
            providers: [
                {
                    provide: review_sessions_service_1.ReviewSessionsService,
                    useValue: mockReviewSessionsService,
                },
            ],
        }).compile();
        controller = module.get(review_sessions_controller_1.ReviewSessionsController);
        service = module.get(review_sessions_service_1.ReviewSessionsService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it('should be defined', () => {
        expect(controller).toBeDefined();
    });
    describe('create', () => {
        it('should create a new review session', async () => {
            const createDto = { deckId: '1' };
            mockReviewSessionsService.create.mockResolvedValue(mockReviewSession);
            const result = await controller.create(createDto, mockRequest);
            expect(result).toEqual(mockReviewSession);
            expect(service.create).toHaveBeenCalledWith(createDto, 'user-1');
        });
    });
    describe('findOne', () => {
        it('should return a review session', async () => {
            mockReviewSessionsService.findOneByUser.mockResolvedValue(mockReviewSession);
            const result = await controller.findOne('1', mockRequest);
            expect(result).toEqual(mockReviewSession);
            expect(service.findOneByUser).toHaveBeenCalledWith('1', 'user-1');
        });
    });
    describe('submitResult', () => {
        it('should submit a review result', async () => {
            const submitDto = {
                flashcardId: '1',
                difficultyRating: entities_1.DifficultyRating.EASY,
            };
            const mockResult = { id: '1', ...submitDto };
            mockReviewSessionsService.submitResult.mockResolvedValue(mockResult);
            const result = await controller.submitResult('1', submitDto, mockRequest);
            expect(result).toEqual(mockResult);
            expect(service.submitResult).toHaveBeenCalledWith('1', submitDto, 'user-1');
        });
    });
    describe('complete', () => {
        it('should complete a review session', async () => {
            const completedSession = { ...mockReviewSession, completedAt: new Date() };
            mockReviewSessionsService.complete.mockResolvedValue(completedSession);
            const result = await controller.complete('1', mockRequest);
            expect(result).toEqual(completedSession);
            expect(service.complete).toHaveBeenCalledWith('1', 'user-1');
        });
    });
    describe('getSummary', () => {
        it('should return session summary', async () => {
            mockReviewSessionsService.getSummary.mockResolvedValue(mockSessionSummary);
            const result = await controller.getSummary('1', mockRequest);
            expect(result).toEqual(mockSessionSummary);
            expect(service.getSummary).toHaveBeenCalledWith('1', 'user-1');
        });
    });
});
//# sourceMappingURL=review-sessions.controller.spec.js.map