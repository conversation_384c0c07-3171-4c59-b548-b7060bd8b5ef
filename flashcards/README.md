# Bird Flashcards App

A full-stack TypeScript application for learning bird identification through interactive flashcards. Built with NestJS backend, Next.js frontend, and PostgreSQL database.

## Features

- 📚 **Deck Management**: Browse and select flashcard decks
- 🎯 **Interactive Review**: Image-first flashcard review with name reveal
- 📊 **Difficulty Tracking**: Rate cards as easy, difficult, or incorrect
- 📈 **Session Summary**: View performance after each review session
- 🔀 **Smart Shuffling**: Random card order for varied practice
- 🐦 **Bird Identification**: Specialized for bird watching and identification

## Project Structure

```
flashcards/
├── backend/          # NestJS API server
│   ├── src/
│   │   ├── entities/     # TypeORM database entities
│   │   ├── decks/        # Deck management endpoints
│   │   └── review-sessions/ # Review session endpoints
├── frontend/         # Next.js React application
├── shared/           # Shared TypeScript types
├── docker-compose.yml # PostgreSQL container setup
└── backend/.env     # Backend environment variables
```

## Prerequisites

- **Node.js** 18+ 
- **Docker Desktop** (for PostgreSQL database)
- **Yarn** 4.0+ (package manager)

## Quick Start

### 1. Install Dependencies
```bash
# Install all workspace dependencies
yarn install

# Alternative: install dependencies for all workspaces
yarn install:all
```

### 2. Start Database
```bash
# Start PostgreSQL container
docker compose up -d postgres

# Verify container is running
docker ps
```

### 3. Start Backend
```bash
# Start NestJS development server
yarn dev:backend
```
The backend will be available at `http://localhost:3001`

### 4. Start Frontend (in new terminal)
```bash
# Start Next.js development server  
yarn dev:frontend
```
The frontend will be available at `http://localhost:3000`

## Environment Configuration

### Backend Environment Variables

The backend requires environment variables in `backend/.env`. Copy the example file and configure:

```bash
cp backend/.env.example backend/.env
```

**Required variables:**
- **Database**: Must match `docker-compose.yml` settings
- **JWT_SECRET**: Generate a secure secret for production
- **Google OAuth**: Get credentials from [Google Console](https://console.developers.google.com/)

**Note**: There is only one `.env` file in `backend/.env` - no root-level `.env` needed.

### 5. Start Both Services
```bash
# Start backend and frontend concurrently
yarn dev
```

## Testing

The project includes comprehensive unit and integration tests for both backend and frontend.

### Run All Tests
```bash
# Run all tests (backend + frontend)
yarn test

# Run all tests with coverage reports
yarn test:coverage
```

### Backend Tests
```bash
# Run backend tests only
yarn test:backend

# Run backend tests in watch mode
yarn workspace backend test --watch

# Run backend tests with coverage
yarn workspace backend test --coverage
```

**Backend test coverage:**
- **29 tests** across 4 test suites
- Unit tests for services and controllers
- Authentication system tests
- Database entity validation

### Frontend Tests
```bash
# Run frontend tests only
yarn test:frontend

# Run frontend tests in watch mode  
yarn workspace frontend test --watch

# Run frontend tests with coverage
yarn workspace frontend test --coverage
```

**Frontend test coverage:**
- **40 tests** across 5 test suites
- Component rendering tests
- Authentication context tests
- API integration tests
- User interaction tests

### Test Requirements
- **PostgreSQL**: Backend tests require the database to be running:
  ```bash
  # Start database before running backend tests
  yarn db:up
  ```
- **Authentication**: Tests include mock authentication setup
- **Environment**: Tests use test-specific environment configuration

### Quick Test Commands
```bash
# Run everything (start DB + run all tests)
yarn db:up && yarn test

# Run tests with coverage reports
yarn test:coverage
```

## Development Commands

### Database Management
```bash
# Start PostgreSQL container
docker compose up -d postgres

# Stop PostgreSQL container
docker compose down

# View database logs
docker logs flashcards-db

# Connect to database directly
docker exec -it flashcards-db psql -U flashcards_user -d flashcards
```

### Backend Development
```bash
# Start in development mode (auto-reload)
yarn workspace backend start:dev

# Build for production
yarn workspace backend build

# Start production build
yarn workspace backend start:prod

# Run in debug mode
yarn workspace backend start:debug
```

### Frontend Development
```bash
# Start development server
yarn workspace frontend dev

# Build for production
yarn workspace frontend build

# Start production server
yarn workspace frontend start

# Run linting
yarn workspace frontend lint
```

### Yarn Workspace Commands
```bash
# Install dependencies for all workspaces
yarn install

# Run a command in a specific workspace
yarn workspace backend [command]
yarn workspace frontend [command]
yarn workspace shared [command]

# Run a command in all workspaces (Yarn 1.x)
yarn workspaces run [command]

# Build specific workspaces
yarn workspace backend build
yarn workspace frontend build
yarn workspace shared build

# Clean all workspaces
yarn clean
```

## Environment Configuration

The application uses the following environment variables (`.env`):

```bash
# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=flashcards_user
DATABASE_PASSWORD=flashcards_pass
DATABASE_NAME=flashcards
NODE_ENV=development
```

## API Documentation

### Decks Endpoints
```bash
# List all decks
GET /decks

# Get specific deck
GET /decks/:id

# Get flashcards for a deck
GET /decks/:id/flashcards
```

### Review Session Endpoints
```bash
# Start new review session
POST /review-sessions
Content-Type: application/json
{ "deckId": "uuid" }

# Get session details
GET /review-sessions/:id

# Submit flashcard rating
POST /review-sessions/:id/results
Content-Type: application/json
{
  "flashcardId": "uuid",
  "difficultyRating": "easy" | "difficult" | "incorrect"
}

# Mark session complete
PATCH /review-sessions/:id/complete

# Get session summary
GET /review-sessions/:id/summary
```

## Testing

### Backend API Testing
```bash
# Test health check (should return empty array initially)
curl http://localhost:3001/decks

# Create a test deck (manual database insertion needed)
# Then test getting flashcards
curl http://localhost:3001/decks/{deck-id}/flashcards

# Test creating review session
curl -X POST http://localhost:3001/review-sessions \
  -H "Content-Type: application/json" \
  -d '{"deckId":"your-deck-id"}'

# Test submitting result
curl -X POST http://localhost:3001/review-sessions/{session-id}/results \
  -H "Content-Type: application/json" \
  -d '{"flashcardId":"your-flashcard-id","difficultyRating":"easy"}'
```

### Manual Database Testing
```bash
# Connect to database
docker exec -it flashcards-db psql -U flashcards_user -d flashcards

# List tables
\dt

# View deck structure
\d decks

# Insert test data
INSERT INTO decks (name, description) VALUES 
('Test Deck', 'A test deck for development');

# View all decks
SELECT * FROM decks;
```

## Database Schema

### Tables
- **decks**: `id (uuid)`, `name`, `description`, `createdAt`
- **flashcards**: `id (uuid)`, `deckId`, `birdName`, `imageUrl`, `createdAt`
- **review_sessions**: `id (uuid)`, `deckId`, `startedAt`, `completedAt`
- **review_results**: `id (uuid)`, `sessionId`, `flashcardId`, `difficultyRating`, `createdAt`

### Relationships
- Deck → Flashcards (1:many)
- Deck → ReviewSessions (1:many)
- ReviewSession → ReviewResults (1:many)
- Flashcard → ReviewResults (1:many)

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL container is running
docker ps

# Restart database container
docker compose restart postgres

# Check database logs
docker logs flashcards-db
```

### Backend Issues
```bash
# Check if port 3001 is available
lsof -i :3001

# Kill any existing process on port 3001
pkill -f "nest start"

# Verify environment variables are loaded
yarn workspace backend start:dev
```

### Docker Issues
```bash
# Restart Docker Desktop
# Check Docker is running
docker --version

# Remove and recreate containers
docker compose down
docker compose up -d postgres
```

## Development Workflow

1. **Start Database**: `docker compose up -d postgres`
2. **Start Backend**: `yarn dev:backend` 
3. **Test API**: Use curl commands or API client
4. **Start Frontend**: `yarn dev:frontend`
5. **Develop Features**: Make changes, server auto-reloads
6. **Test Integration**: Test full user workflow

## Contributing

1. Ensure all tests pass
2. Follow TypeScript best practices
3. Use existing code patterns and conventions
4. Update documentation for new features