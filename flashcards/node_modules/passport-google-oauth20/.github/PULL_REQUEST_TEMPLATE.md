** READ THIS FIRST! **

#### Are you implementing a new feature?

Requests for new features should first be discussed on the [developer forum](https://github.com/passport/develop).
This allows the community to gather feedback and assess whether or not there is
an existing way to achieve the desired functionality.

If it is determined that a new feature needs to be implemented, include a link
to the relevant discussion along with the pull request.

#### Is this a security patch?

Do not open pull requests that might have security implications.  Potential
security vulnerabilities should be reported privately to jared<PERSON><PERSON>@gmail.com.
Once any vulerabilities have been repaired, the details will be disclosed
publicly in a responsible manner.  This also allows time for coordinating with
affected parties in order to mitigate negative consequences.


If neither of the above two scenarios apply to your situation, you should open
a pull request.  Delete this paragraph and the text above, and fill in the
information requested below.

<!-- Provide a brief summary of the request in the title field above. -->

<!-- Provide a detailed description of your use case, including as much -->
<!-- detail as possible about what you are trying to accomplish and why. -->
<!-- If this patch closes an open issue, include a reference to the issue -->
<!-- number. -->

### Checklist

<!-- Place an `x` in the boxes that apply.  If you are unsure, please ask and -->
<!-- we will help. -->

- [ ] I have read the [CONTRIBUTING](https://github.com/jaredhanson/passport-google-oauth2/blob/master/CONTRIBUTING.md) guidelines.
- [ ] I have added test cases which verify the correct operation of this feature or patch.
- [ ] I have added documentation pertaining to this feature or patch.
- [ ] The automated test suite (`$ make test`) executes successfully.
- [ ] The automated code linting (`$ make lint`) executes successfully.
