{"name": "passport-strategy", "version": "1.0.0", "description": "An abstract class implementing Passport's strategy API.", "keywords": ["passport", "strategy"], "repository": {"type": "git", "url": "git://github.com/jaredhanson/passport-strategy.git"}, "bugs": {"url": "http://github.com/jared<PERSON>son/passport-strategy/issues"}, "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "main": "./lib", "dependencies": {}, "devDependencies": {"mocha": "1.x.x", "chai": "1.x.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "node_modules/.bin/mocha --reporter spec --require test/bootstrap/node test/*.test.js"}, "testling": {"browsers": ["chrome/latest"], "harness": "mocha", "files": ["test/bootstrap/testling.js", "test/*.test.js"]}}